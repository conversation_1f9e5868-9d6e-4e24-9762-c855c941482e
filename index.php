<?php
//ERROR_REPORTING(E_ERROR);
use system\router;

echo "<!-- starting systems -->";
header("Cache-Control: no-store, no-cache, must-revalidate"); // HTTP/1.1
header("Cache-Control: post-check=0, pre-check=0", false);
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT"); // Date in the past
header("Pragma: no-cache"); // HTTP/1.0
header("Last-Modified: " . gmdate("D, d M Y H:i:s") . " GMT");
const DS = DIRECTORY_SEPARATOR;
$path['fs_app_root'] = __DIR__;
//echo $path['fs_app_root'] . DS . 'system'. DS . 'config'. DS . 'path_schema.php';
$schema = require_once $path['fs_app_root'] . DS . 'system'. DS . 'config'. DS . 'path_schema.php';
$ds = DS;
$path['fs_system'] = "{$path['fs_app_root']}{$ds}{$schema['system']['root']}" ;

$input_params = array_merge($_GET, $_POST);

define('INPUT_PARAMS',$input_params);
const DEBUG_MODE = true;
const API_RUN = false;
require_once $path['fs_system'] . DS . 'classes'. DS . 'startup_sequence.class.php';
require_once $path['fs_system'] . DS . 'functions'. DS . 'functions.php';
echo "<!-- starting ss -->";
startup_sequence::start($path,$schema);


print_rr("starting route");
echo "<!-- starting route -->";
echo router::route();
echo "<!-- end route -->";