-- Add <PERSON><PERSON>b <PERSON> to navigation
INSERT INTO `autobooks_navigation` (`parent_path`, `route_key`, `name`, `icon`, `file_path`, `required_roles`, `sort_order`, `show_navbar`, `can_delete`, `is_system`, `created_at`, `updated_at`) VALUES
('system', 'modal_tab_demo', 'Modal Tab Demo', 'window', 'modal_tab_demo', '[]', 20, 1, 1, 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE
`name` = VALUES(`name`),
`icon` = VALUES(`icon`),
`file_path` = VALUES(`file_path`),
`is_system` = VALUES(`is_system`),
`show_navbar` = VALUES(`show_navbar`),
`updated_at` = NOW();

-- Verify the entry was added
SELECT `route_key`, `name`, `file_path`, `is_system`, `show_navbar` 
FROM `autobooks_navigation` 
WHERE `route_key` = 'modal_tab_demo' 
AND `parent_path` = 'system';
