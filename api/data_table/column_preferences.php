<?php
require_once '../../system/autoload.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Only handle POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    $table_name = $_POST['table_name'] ?? '';
    $callback = $_POST['callback'] ?? '';
    $hidden_columns = json_decode($_POST['hidden_columns'] ?? '[]', true);
    $column_structure = json_decode($_POST['column_structure'] ?? '[]', true);

    if (empty($table_name)) {
        throw new Exception('Table name is required');
    }

    // Store preferences in session for now (could be moved to database later)
    $preference_key = "column_preferences_{$table_name}";
    $_SESSION[$preference_key] = [
        'hidden' => $hidden_columns,
        'structure' => $column_structure,
        'updated_at' => date('Y-m-d H:i:s')
    ];

    // If callback is provided, call it to regenerate the table
    if (!empty($callback)) {
        // Include the data table API functions
        require_once '../../system/api/data_table.api.php';

        // Prepare parameters for the callback
        $params = array_merge($_POST, [
            'table_name' => $table_name,
            'callback' => $callback
        ]);

        // Remove column preference parameters from the callback params
        unset($params['hidden_columns'], $params['column_order']);

        // Call the data table filter function which will handle the callback
        $result = \api\data_table\data_table_filter($params);

        // Return the updated table HTML
        echo $result;
    } else {
        // Just return success if no callback
        echo json_encode([
            'success' => true,
            'message' => 'Column preferences saved',
            'preferences' => $_SESSION[$preference_key]
        ]);
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'error' => $e->getMessage()
    ]);
}
?>
