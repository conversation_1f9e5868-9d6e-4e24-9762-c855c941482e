@props([
    'title' => 'main view',
    'description' => '',
    'view' => 'dashboard'
])
<x-layout-head />
<body class="h-full" x-data="{
    showModal: false,
    tabs: [],
    currentTab: null,
    nextTabId: 1,

    addTab(title, content, url = '') {
        const tabId = this.nextTabId++;
        const newTab = {
            id: tabId,
            title: title || 'New Tab',
            content: content || '',
            url: url,
            pinned: false
        };

        // If modal is closed or no pinned tabs exist, replace all tabs
        if (!this.showModal || !this.tabs.some(tab => tab.pinned)) {
            this.tabs = [newTab];
        } else {
            // Modal is open with pinned tabs, add new tab
            this.tabs.push(newTab);
        }

        this.currentTab = tabId;
        this.showModal = true;
    },

    switchTab(tabId) {
        this.currentTab = tabId;
    },

    closeTab(tabId) {
        const tabIndex = this.tabs.findIndex(tab => tab.id === tabId);
        if (tabIndex === -1) return;

        this.tabs.splice(tabIndex, 1);

        // If we closed the current tab, switch to another
        if (this.currentTab === tabId) {
            if (this.tabs.length > 0) {
                this.currentTab = this.tabs[this.tabs.length - 1].id;
            } else {
                this.showModal = false;
                this.currentTab = null;
            }
        }
    },

    togglePin(tabId) {
        const tab = this.tabs.find(tab => tab.id === tabId);
        if (tab) {
            tab.pinned = !tab.pinned;
        }
    },

    getCurrentTabContent() {
        const tab = this.tabs.find(tab => tab.id === this.currentTab);
        return tab ? tab.content : '';
    }
}" x-bind:class="showModal && 'overflow-hidden'"
   @modal-add-tab.window="addTab($event.detail.title, $event.detail.content, $event.detail.url)"
   @modal-update-tab.window="
       const tab = tabs.find(t => t.id === currentTab);
       if (tab) {
           tab.content = $event.detail.content;
           if ($event.detail.title) tab.title = $event.detail.title;
       }
   ">
@php
    print_rr($view,'launched main edge view with:');
@endphp
    <x-notification-handler />
    <div class="bg-gray-100">
        <div x-data="sidebarState()" x-init="initialize()" @keydown.window.escape="open = false">
            <div class="hidden lg:w-56 lg:fixed lg:inset-y-0 lg:z-40 lg:flex lg:flex-col">
                <x-layout-sidebar />
            </div>
            <div id="content_wrapper" class="h-screen flex flex-col transition-all duration-300" :class='collapsed ? "lg:pl-11" : "lg:pl-44"'>
                <!--  Edge::render('navbar');  -->
                @php
                    print_rr($view,'main edge view');
                @endphp
                <x-layout-view :view='$view' :view_content='$view_content ?? null' />
            </div>
            <x-layout-footer />
        </div>
    </div>
<script>function sidebarState() {return {collapsed: false,initialize() {const cookieValue = document.cookie.split('; ').find(row => row.startsWith('sidebar-collapsed='))?.split('=')[1];this.collapsed = cookieValue === 'true';},toggle() {this.collapsed = !this.collapsed;document.cookie = `sidebar-collapsed=${this.collapsed}; path=/; max-age=31536000`;},};}</script>