@props([
    'columns' => [],
    'table_name' => '',
    'db_table' => '',
    'callback' => '',
    'column_preferences' => [],
    'available_fields' => ''
])

@php
// Generate unique column IDs and prepare column structure
$processed_columns = [];
foreach ($columns as $index => $col) {
    $column_id = 'col_' . $index . '_' . md5($col['label']);
    $processed_columns[] = [
        'id' => $column_id,
        'label' => $col['label'],
        'field' => $col['field'],
        'filter' => $col['filter'] ?? false,
        'fields' => is_array($col['field']) ? $col['field'] : [$col['field']], // Always array for consistency
        'parent' => null // Top-level columns have no parent
    ];
}

// Process existing preferences to match new structure
$column_structure = $column_preferences['structure'] ?? [];
$hidden_columns = $column_preferences['hidden'] ?? [];
$column_string = '';

    if (!empty($available_fields)) {
        $column_string = count($available_fields) > 0 ? '["' . implode('","', $available_fields) . '"]' : '[]';
    } else if (!empty($db_table)) {
        $db_columns = database::table($db_table)->getColumns();
        $column_string = count($db_columns) > 0 ? '["' . implode('","', $db_columns) . '"]' : '[]';
    } else {
        $column_string = '[]'; // Default to empty array if no fields available
    }
@endphp
<div x-data='{
    open: false,
    columns: {{ json_encode($processed_columns) }},
    columnStructure: {{ json_encode($column_structure) }},
    hiddenColumns: {{ json_encode($hidden_columns) }},
    availableFields: {{ $column_string }},
    newColumnName: "",
    newFieldName: "",
    showAddColumn: false,
    showAddField: false,

    toggleColumn(columnId) {
        const index = this.hiddenColumns.indexOf(columnId);
        if (index > -1) {
            this.hiddenColumns.splice(index, 1);
        } else {
            this.hiddenColumns.push(columnId);
        }
        this.savePreferences();
    },

    isColumnVisible(columnId) {
        return !this.hiddenColumns.includes(columnId);
    },

    getColumnById(columnId) {
        return this.columns.find(col => col.id === columnId);
    },

    moveFieldToColumn(fieldName, targetColumnId, sourceColumnId = null) {
        // Remove field from source column if specified
        if (sourceColumnId) {
            const sourceCol = this.getColumnById(sourceColumnId);
            if (sourceCol) {
                const fieldIndex = sourceCol.fields.indexOf(fieldName);
                if (fieldIndex > -1) {
                    sourceCol.fields.splice(fieldIndex, 1);
                }
            }
        }

        // Add field to target column
        const targetCol = this.getColumnById(targetColumnId);
        if (targetCol && !targetCol.fields.includes(fieldName)) {
            targetCol.fields.push(fieldName);
        }

        this.savePreferences();
    },

    removeFieldFromColumn(fieldName, columnId) {
        const column = this.getColumnById(columnId);
        if (column) {
            const fieldIndex = column.fields.indexOf(fieldName);
            if (fieldIndex > -1) {
                column.fields.splice(fieldIndex, 1);
            }
        }
        this.savePreferences();
    },

    addNewColumn() {
        if (!this.newColumnName.trim()) return;

        const newId = "col_new_" + Date.now();
        const newColumn = {
            id: newId,
            label: this.newColumnName.trim(),
            fields: [],
            filter: false,
            visible: true
        };

        this.columns.push(newColumn);
        this.newColumnName = "";
        this.showAddColumn = false;
        this.savePreferences();
    },

    removeColumn(columnId) {
        const index = this.columns.findIndex(col => col.id === columnId);
        if (index > -1) {
            this.columns.splice(index, 1);
            // Also remove from hidden columns if it was there
            const hiddenIndex = this.hiddenColumns.indexOf(columnId);
            if (hiddenIndex > -1) {
                this.hiddenColumns.splice(hiddenIndex, 1);
            }
            this.savePreferences();
        }
    },

    addFieldToColumn(fieldName, columnId) {
        const column = this.getColumnById(columnId);
        if (column && !column.fields.includes(fieldName)) {
            column.fields.push(fieldName);
            this.savePreferences();
        }
    },

    addNewField() {
        if (!this.newFieldName.trim()) return;

        // Add to available fields if not already there
        if (!this.availableFields.includes(this.newFieldName.trim())) {
            this.availableFields.push(this.newFieldName.trim());
        }

        this.newFieldName = "";
        this.showAddField = false;
    },

    savePreferences() {
        // Build the column structure for server
        const structure = this.columns.map(col => ({
            id: col.id,
            label: col.label,
            fields: col.fields,
            filter: col.filter,
            visible: this.isColumnVisible(col.id)
        }));

        // Send preferences to server via HTMX
        htmx.ajax("POST", "{{ APP_ROOT }}/api/data_table/column_preferences/save", {
            values: {
                table_name: "{{ $table_name }}",
                callback: "{{ $callback }}",
                hidden_columns: JSON.stringify(this.hiddenColumns),
                column_structure: JSON.stringify(structure)
            },
            target: ".data_table",
            swap: "outerHTML"
        });
    },
    initSortable() {
        this.$nextTick(() => {
            const container = this.$refs.columnList;
            if (container && window.Sortable) {
                // Main column sorting
                new Sortable(container, {
                    animation: 150,
                    handle: ".column-drag-handle",
                    filter: ".field-item", // Don"t drag field items when dragging columns
                    onEnd: (evt) => {
                        // Update column order based on new positions
                        const newOrder = [];
                        const items = container.querySelectorAll("[data-column-id]");
                        items.forEach(item => {
                            const columnId = item.dataset.columnId;
                            const column = this.getColumnById(columnId);
                            if (column) {
                                newOrder.push(column);
                            }
                        });
                        this.columns = newOrder;
                        this.savePreferences();
                    }
                });

                // Field sorting within columns
                container.querySelectorAll(".field-container").forEach(fieldContainer => {
                    new Sortable(fieldContainer, {
                        group: "fields", // Allow dragging between columns
                        animation: 150,
                        handle: ".field-drag-handle",
                        onEnd: (evt) => {
                            const fieldName = evt.item.dataset.fieldName;
                            const targetColumnId = evt.to.closest("[data-column-id]").dataset.columnId;
                            const sourceColumnId = evt.from.closest("[data-column-id]").dataset.columnId;

                            if (targetColumnId !== sourceColumnId) {
                                this.moveFieldToColumn(fieldName, targetColumnId, sourceColumnId);
                            }
                        }
                    });
                });
            }
        });
    }
}'
x-init="$nextTick(() => initSortable())"
class="relative inline-block text-left"
@click.away="open = false">

    <!-- Column Manager Button -->
    <button type="button"
            @click="open = !open; if(open) $nextTick(() => initSortable())"
            class="inline-flex items-center gap-x-1.5 rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
            aria-expanded="false"
            aria-haspopup="true">
        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 4.5v15m6-15v15m-10.875 0h15.75c.621 0 1.125-.504 1.125-1.125V5.625c0-.621-.504-1.125-1.125-1.125H4.125C3.504 4.5 3 5.004 3 5.625v13.5c0 .621.504 1.125 1.125 1.125z" />
        </svg>
        Columns
        <svg class="h-4 w-4" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
        </svg>
    </button>

    <!-- Dropdown Panel -->
    <div x-show="open"
         x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         class="absolute right-0 z-20 mt-2 w-96 max-h-screen origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden flex flex-col"
         style="height: 80vh;"
         role="menu"
         aria-orientation="vertical"
         tabindex="-1">
        
        <!-- Header -->
        <div class="p-4 border-b border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between mb-3">
                <h3 class="text-sm font-medium text-gray-900">Manage Columns</h3>
                <div class="flex gap-2">
                    <button type="button"
                            @click="showAddField = !showAddField"
                            class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200">
                        + Field
                    </button>
                    <button type="button"
                            @click="showAddColumn = !showAddColumn"
                            class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                        + Column
                    </button>
                    <button type="button"
                            @click="hiddenColumns = []; savePreferences()"
                            class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                        Show All
                    </button>
                </div>
            </div>

            <!-- Add Field Form -->
            <div x-show="showAddField" x-transition class="mb-3 p-2 bg-green-50 rounded border">
                <div class="flex gap-2 items-center">
                    <input type="text"
                           x-model="newFieldName"
                           placeholder="Field name (e.g., phone)"
                           class="flex-1 text-xs px-2 py-1 border border-gray-300 rounded">
                    <button type="button"
                            @click="addNewField()"
                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                        Add
                    </button>
                    <button type="button"
                            @click="showAddField = false"
                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                        Cancel
                    </button>
                </div>
            </div>

            <!-- Add Column Form -->
            <div x-show="showAddColumn" x-transition class="mb-3 p-2 bg-blue-50 rounded border">
                <div class="flex gap-2 items-center">
                    <input type="text"
                           x-model="newColumnName"
                           placeholder="Column name (e.g., Contact Info)"
                           class="flex-1 text-xs px-2 py-1 border border-gray-300 rounded">
                    <button type="button"
                            @click="addNewColumn()"
                            class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                        Add
                    </button>
                    <button type="button"
                            @click="showAddColumn = false"
                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                        Cancel
                    </button>
                </div>
            </div>

            <div class="text-xs text-gray-500">
                Drag columns to reorder • Drag fields between columns to combine • Click to show/hide
            </div>
        </div>

        <!-- Column List -->
        <div class="flex-1 overflow-y-auto p-4">
            <div x-ref="columnList" class="space-y-3">
                <template x-for="column in columns" :key="column.id">
                    <div class="border border-gray-200 rounded-lg bg-white"
                         :data-column-id="column.id">

                        <!-- Column Header -->
                        <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">
                            <!-- Column Drag Handle -->
                            <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                </svg>
                            </div>

                            <!-- Visibility Checkbox -->
                            <label class="flex items-center flex-1 cursor-pointer">
                                <input type="checkbox"
                                       :checked="isColumnVisible(column.id)"
                                       @change="toggleColumn(column.id)"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                <span class="text-sm font-medium text-gray-900" x-text="column.label"></span>
                            </label>

                            <!-- Column Type Indicator -->
                            <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"
                                      :class="column.filter ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'">
                                    <span x-text="column.filter ? 'Filterable' : 'Display'"></span>
                                </span>
                            </div>

                            <!-- Field Count Badge -->
                            <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700"
                                      x-text="column.fields.length + ' field' + (column.fields.length !== 1 ? 's' : '')">
                                </span>
                            </div>

                            <!-- Remove Column Button -->
                            <button type="button"
                                    @click="removeColumn(column.id)"
                                    class="ml-2 text-red-400 hover:text-red-600"
                                    title="Remove Column">
                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                            </button>
                        </div>

                        <!-- Fields Container -->
                        <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">
                            <!-- Add Field Dropdown -->
                            <div class="mb-2">
                                <select @change="if($event.target.value) { addFieldToColumn($event.target.value, column.id); $event.target.value = ''; }"
                                        class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                    <option value="">+ Add field...</option>
                                    <template x-for="field in availableFields" :key="field">
                                        <option :value="field"
                                                x-show="!column.fields.includes(field)"
                                                x-text="field"></option>
                                    </template>
                                </select>
                            </div>

                            <!-- Existing Fields -->
                            <template x-for="field in column.fields" :key="field">
                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     :data-field-name="field">
                                    <!-- Field Drag Handle -->
                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700" x-text="field"></span>
                                    <!-- Remove Field Button -->
                                    <button type="button"
                                            @click="removeFieldFromColumn(field, column.id)"
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                            </template>

                            <!-- Drop Zone Indicator -->
                            <div class="text-xs text-gray-400 text-center py-2"
                                 x-show="column.fields.length === 0">
                                Drop fields here or use dropdown above
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <!-- Footer Actions -->
        <div class="p-4 border-t border-gray-200 bg-gray-50">
            <div class="flex justify-between items-center">
                <div class="flex gap-2 items-center">
                    <button type="button"
                            @click="hiddenColumns = columns.map(c => c.id); savePreferences()"
                            class="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                        Hide All
                    </button>
                    <span class="text-xs text-gray-500" x-text="columns.length + ' columns, ' + columns.reduce((sum, col) => sum + col.fields.length, 0) + ' fields'"></span>
                </div>
                <button type="button"
                        @click="open = false"
                        class="text-xs px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                    Done
                </button>
            </div>
        </div>
    </div>
</div>
