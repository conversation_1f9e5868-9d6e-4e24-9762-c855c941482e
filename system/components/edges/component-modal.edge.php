@props([
    'title' => 'model',
    'description' => 'A model',
    'content' => [], // An array of [('header'|'body'|'footer') => 'content']
    'title' => '',
])

<!-- Modal Container -->
<div x-show="showModal"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     class="fixed inset-0 z-50 overflow-y-auto"
     style="display: none;">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100"
                x-transition:leave-end="opacity-0"
                class="fixed inset-0 transition-opacity">
            <div class="absolute inset-0 bg-gray-500 opacity-75" @click="showModal = false"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

        <div
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-7xl sm:w-full sm:p-6">

            <!-- Tab Bar -->
            <div x-show="tabs.length > 0" class="border-b border-gray-200 mb-4">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <template x-for="tab in tabs" :key="tab.id">
                        <div class="flex items-center group">
                            <button
                                @click="switchTab(tab.id)"
                                :class="{
                                    'border-indigo-500 text-indigo-600': currentTab === tab.id,
                                    'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': currentTab !== tab.id
                                }"
                                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center"
                                x-text="tab.title">
                            </button>

                            <!-- Pin Button -->
                            <button
                                @click="togglePin(tab.id)"
                                :class="{
                                    'text-indigo-600': tab.pinned,
                                    'text-gray-400 hover:text-gray-600': !tab.pinned
                                }"
                                class="ml-2 p-1 rounded hover:bg-gray-100"
                                :title="tab.pinned ? 'Unpin tab' : 'Pin tab'">
                                <!-- Unpinned icon (circle) -->
                                <svg x-show="!tab.pinned" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <!-- Pinned icon (pin) -->
                                <svg x-show="tab.pinned" class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8zM9 5a2 2 0 00-2 2v6H6a1 1 0 100 2h8a1 1 0 100-2h-1V7a2 2 0 00-2-2H9zM7 8a1 1 0 012-2h2a1 1 0 110 2H9a1 1 0 01-2 0z" clip-rule="evenodd"/>
                                </svg>
                            </button>

                            <!-- Close Button -->
                            <button
                                @click="closeTab(tab.id)"
                                class="ml-2 p-1 rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100"
                                title="Close tab">
                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                    </template>
                </nav>
            </div>

            <!-- Global Close Button -->
            <div class="absolute top-0 right-0 pt-4 pr-4">
                <button type="button"
                        @click="showModal = false; tabs = []; currentTab = null;"
                        class="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <span class="sr-only">Close</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>

            <!-- Tab Content -->
            <div id="modal_body">
                <template x-for="tab in tabs" :key="tab.id">
                    <div x-show="currentTab === tab.id" x-html="tab.content"></div>
                </template>
                <!-- Fallback for legacy content loading -->
                <div x-show="tabs.length === 0" id="modal_body_legacy">
                    <!-- Modal content will be loaded here for legacy usage -->
                </div>
            </div>
        </div>
    </div>
</div>


