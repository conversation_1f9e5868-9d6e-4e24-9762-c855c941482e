<?php
namespace data_table;
use edge\Edge;



/**
 * Criteria Schema

function get_criteria_schema(): array {
    return [
        // Main query parameters
        'limit' => 30,                   // int: Number of records to return
        'offset' => 0,                   // int: Starting point for fetching records
        'order_by' => 'field_name',      // string: Column name to sort by
        'order_direction' => 'ASC',      // string: Sort direction (ASC or DESC)

        // Where conditions
        'where' => [
            'table.column_name' => ['=', 'value'],
            'and' => [
                'table.column_name' => ['=', 'value'],// Array: [operator, value]
            ],

            'or' => [
                'table.column_name' => ['=', 'value'],// Array: [operator, value]
            ]
            // Possible operators: =, !=, >, <, >=, <=, LIKE, IN, NOT IN, IS NULL, IS NOT NULL
        ],

        // Search parameters
        'search' => [
            'term' => '',                // string: Search term
            'columns' => [               // array: Columns to search in
                'table1.column1',
                'table2.column2'
            ]
        ],

        // Join conditions
        'joins' => [
            [
                'type' => 'LEFT',        // string: JOIN type (LEFT, RIGHT, INNER)
                'table' => 'table_name', // string: Table to join
                'on' => [                // array: Join conditions
                    'table1.id' => 'table2.foreign_id'
                ]
            ]
        ],

        // Group by
        'group_by' => [                 // array: Columns to group by
            'table.column1',
            'table.column2'
        ],

        // Having conditions (for grouped results)
        'having' => [
            'column' => ['>', 0]        // Array: [operator, value]
        ],

        // Search columns for full-text search
        'search_columns' => [           // array: Columns to include in full-text search
            'table1.column1',
            'table2.column2'
        ],

        // Distinct selection
        'distinct' => true,             // boolean: Whether to select distinct records

        // Custom SQL fragments
        'custom_sql' => '',             // string: Raw SQL to append to query

        // Debug mode
        'debug' => false                // boolean: Whether to return query string instead of executing
    ];
}


$example_criteria = [
    'limit' => 10,
    'order_by' => 'subs_enddatediff',
    'where' => [
        'subs.status' => ['=', 'Active'],
        'subs.endDate' => ['>', date('Y-m-d')]
    ],
    'search_columns' => [
        'subs.subscriptionReferenceNumber',
        'subs.offeringName',
        'endcust.name',
        'endcust.email'
    ],
    'joins' => [
        [
            'type' => 'LEFT',
            'table' => 'endcust',
            'on' => ['subs.customer_id' => 'endcust.id']
        ]
    ]
];

*/
class data_table {
    public static array $table_structure;
    public static array $data;
    public static string $db_table;
    public static array $distinct_columns;
    public static array $replacements;
    public static array $criteria;
    public static array $cols_hidden;
    public static bool $just_body;
    public static bool $just_rows;
    public static bool $just_table;
    public static bool $htmx_oob;
    public static string $callback;
    public static string $db_name;
    public static array $available_fields;

    public static function process_data_table(
        array $table_structure,
        array $data,
        string $db_table = '',
        string $callback = '',
        array $replacements = [],
        array $criteria = [],
        array $cols_hidden = [],
        bool $just_body = false,
        bool $just_rows = false,
        bool $just_table = false,
        $htmx_oob = false,
        int $total_count = null,
        string $table_name = '',
        array $available_fields = []
    ): string {
        self::$table_structure = $table_structure;
        self::$db_table = $db_table ?? $table_structure['db_table'] ?? '';
        self::$available_fields = $available_fields;
        self::$data = $data;
        self::$callback = ($callback == '') ? debug_backtrace()[0]['function'] : $callback;
        self::$replacements = $replacements;
        self::$criteria = $criteria;
        self::$cols_hidden = $cols_hidden;
        self::$just_body = $just_body;
        self::$just_rows = $just_rows;
        self::$just_table = $just_table;
        self::$htmx_oob = $htmx_oob;


//        foreach ($cols_hidden as $col) {
//            unset($row_content[$col]);
//        }

        if ($htmx_oob) $row = true;

        // build_column_filters

        self::process_column_filters();
        self::process_string_replacements();

        $htmx_oob_out = "";
        if ($htmx_oob) {
            $htmx_oob_out = [
                "hx-swap-oob" => "true",
                "hx-ext" => "class-tools",
                "hx-classes" => "add htmx-settling, remove:htmx-settling:10s"
            ];
        }

        $sort_column = $criteria['order_by']['column'] ?? self::$table_structure['columns'][0]['field'];
        $sort_direction = $criteria['order_by']['direction'] ?? 'ASC';

        // Get column preferences from session
        $column_preferences = [];
        if (!empty($table_name)) {
            $preference_key = "column_preferences_{$table_name}";
            $column_preferences = $_SESSION[$preference_key] ?? [];
        }

        // Apply column structure if preferences exist
        $columns = self::build_column_filters();
        if (!empty($column_preferences['structure'])) {
            $columns = self::apply_column_structure($columns, $column_preferences['structure']);
        }

        $data = [
            "title" => "subscriptions",
            "description" => "",
            "items" => self::$data,
            "columns" => $columns,
            "available_fields" => self::$available_fields,
            "rows" => [
                'id' => 'subscription_id_' . $items['id'],
                'extra_parameters' => $htmx_oob_out,
            ],
            "just_body" => $just_body,
            "just_rows" => $just_rows,
            "just_table" => $just_table,
            "sort_column" => $sort_column,
            "sort_direction" => $sort_direction,
            "callback" => $callback,
            "table_name" => $table_name,
            "column_preferences" => $column_preferences
        ];

        // Add total count for pagination if provided
        if ($total_count !== null) {
            $data['total_count'] = $total_count;
        }
       // print_rr($data, 'datad');
        if ($just_body) return Edge::render('data-table', $data);

        return Edge::render('data-table', $data);
    }
    public static function data_table_filter($p,$callback){
        $criteria = data_table::api_process_criteria($p);
        return $callback(criteria: $criteria);
    }

    public static function filter_db_schema(): array {
        $db = [];
        foreach (self::$table_structure['columns'] as $column) {
            if (is_array($column['field'])) {
                foreach ($column['field'] as $key => $sub_field) $db[] = $sub_field;
                continue;
            }
            $db[] = $column['field'];
        }
        return $db;
    }

    public static function api_process_criteria($input) {
        print_rr($input, 'api_process_criteria');
        $cols = $criteria = [];

        // Handle pagination parameters
        if (isset($input['limit'])) {
            $criteria['limit'] = (int)$input['limit'];
        }
        if (isset($input['offset'])) {
            $criteria['offset'] = (int)$input['offset'];
        }
        if (isset($input['page'])) {
            $criteria['page'] = (int)$input['page'];
        }
        if (isset($input['pagination_mode'])) {
            $criteria['pagination_mode'] = $input['pagination_mode'];
        }

        // Handle search terms
        if (isset($input['search_terms']) && !empty($input['search_terms'])) {
            $criteria['search'] = $input['search_terms'];
        }

        // Handle sorting
        if (isset($input['sort_column']) && !empty($input['sort_column'])) {
            $criteria['order_by'] = $input['sort_column'];
            $criteria['order_direction'] = $input['sort_direction'] ?? 'asc';
        }

        if (isset($input['column'])) {
            foreach ($input['column'] as $column => $value) {
                $value = $value['multi'];
                if (empty($value)) continue;
                print_rr($column, ' val: ' . $value);

                $col_parts = explode("_", $column, 2);
                $table = $col_parts[0];
                $column_name = $col_parts[1];
                if (strpos($value, ',')) {
                    $value_parts = explode(",", $value);
                    foreach ($value_parts as $value_b) {
                        $value_b = trim($value_b);
                        $cols['OR'][] = ["{$table}.{$column_name}",'=', $value_b];
                    }
                } else {
                    $cols["{$table}.{$column_name}"] = ['=', $value];
                }
            }
            if (count($cols) > 0) $criteria["where"] = $cols;
        }
        if (isset($input['search_terms']) && $input['search_terms'] != '') $criteria["search"] = $input['search_terms'];
        if (isset($input['order_by'])) {
            $criteria["order_by"]['column'] = $input['order_by'];
            if (isset($input['order_direction'])) $criteria["order_by"]["direction"] = $input['order_direction'];
        }
        return $criteria;
    }



    public static function build_column_filters(): array {
        $columns = self::process_auto_filters(self::$table_structure['columns']);
        $dsucpunt = 0;
        foreach ($columns as $key => $col) {
            $count = 1;
            $dsucpunt++;
            $col_field = str_replace('_', '.', $col['field'], $count);
            if (is_string($col_field)) $columns[$key]['selected'] = $criteria['where'][$col_field][1] ?? $col['label'];
            //$columns[$key]['selected'] = $criteria['where'][$col_field][1] ?? $col['label'];
        }
        print_rr($columns, 'build column filters end');
        return $columns;
    }

    public static function process_auto_filters(): array {
        $columns = self::$table_structure['columns'];
        foreach ($columns as $key => $col) {
            $filter_criteria = [
                "order_by" => $col['field'],
                'limit' => $col['filter_limit'] ?? 10
            ];
            if (isset($col['auto_filter']) && is_array($col['filter_data']) && count($col['filter_data']) > 0) {
                $filter_assoc = array_combine($col['filter_data'], $col['filter_data']);
                $columns[$key]['filters'] = $filter_assoc;
                unset($columns[$key]['filter_data']);
            }
        }
        return $columns;
    }


    public static function process_string_replacements(): void {
        foreach (self::$data as &$item) {
            foreach (self::$replacements as $field => $replacements) {
                if (is_string($item[$field])) {
                    $item[$field] = strtr($item[$field], $replacements);
                }
            }
            //$item[$field] = strtr($item[$field], $replacements);
        }
        unset($item);
    }

    public static function process_column_filters() {
        foreach (self::$table_structure['columns'] as $key => $col) {
            if (isset($col['string_replacements'])) self::process_string_replacements();
            if (isset($col['auto_filter'])) {
                if (isset($col['string_replacements'])) {
                    foreach (self::$table_structure['columns'][$key]['filter_data'] as &$item) {
                        if (is_string($item)) {
                            $item = strtr($item, $col['string_replacements']);
                        }
                    }
                }
            }
        }
    }

    /**
     * Apply column structure based on user preferences
     * This handles column ordering, field combinations, and visibility
     */
    public static function apply_column_structure(array $original_columns, array $structure): array {
        if (empty($structure)) {
            return $original_columns;
        }

        $processed_columns = [];
        $original_by_field = [];

        // Index original columns by their field(s) for lookup
        foreach ($original_columns as $column) {
            if (is_array($column['field'])) {
                // For multi-field columns, index by each field
                foreach ($column['field'] as $field) {
                    $original_by_field[$field] = $column;
                }
            } else {
                $original_by_field[$column['field']] = $column;
            }
        }

        // Process each column in the structure
        foreach ($structure as $struct_col) {
            if (!$struct_col['visible']) {
                continue; // Skip hidden columns
            }

            // Create new column based on structure
            $new_column = [
                'label' => $struct_col['label'],
                'field' => count($struct_col['fields']) === 1 ? $struct_col['fields'][0] : $struct_col['fields'],
                'filter' => $struct_col['filter'] ?? false,
                'extra_parameters' => ''
            ];

            // Copy any additional properties from original column
            $first_field = $struct_col['fields'][0] ?? '';
            if (isset($original_by_field[$first_field])) {
                $original = $original_by_field[$first_field];
                $new_column['replacements'] = $original['replacements'] ?? null;
                $new_column['content'] = $original['content'] ?? null;
                $new_column['auto_filter'] = $original['auto_filter'] ?? null;
                $new_column['filter_data'] = $original['filter_data'] ?? null;
            }

            $processed_columns[] = $new_column;
        }

        // Add any original columns that weren't in the structure
        foreach ($original_columns as $original) {
            $original_fields = is_array($original['field']) ? $original['field'] : [$original['field']];
            $found_in_structure = false;

            foreach ($structure as $struct_col) {
                if (array_intersect($original_fields, $struct_col['fields'])) {
                    $found_in_structure = true;
                    break;
                }
            }

            if (!$found_in_structure) {
                $processed_columns[] = $original;
            }
        }

        return $processed_columns;
    }
}
