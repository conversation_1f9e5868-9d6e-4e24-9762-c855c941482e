<?php

class ModalTabManager {
    
    /**
     * Create a new tab with content
     * 
     * @param string $title Tab title
     * @param string $content Tab content (HTML)
     * @param string $url Optional URL for the tab
     * @return string JavaScript to dispatch tab creation event
     */
    public static function createTab($title, $content, $url = '') {
        $tabData = [
            'title' => $title,
            'content' => $content,
            'url' => $url
        ];
        
        return self::dispatchEvent('modal-add-tab', $tabData);
    }
    
    /**
     * Update the current tab's content
     * 
     * @param string $content New content for the current tab
     * @param string $title Optional new title for the tab
     * @return string JavaScript to dispatch tab update event
     */
    public static function updateCurrentTab($content, $title = null) {
        $tabData = ['content' => $content];
        if ($title !== null) {
            $tabData['title'] = $title;
        }
        
        return self::dispatchEvent('modal-update-tab', $tabData);
    }
    
    /**
     * Generate HTMX response with tab functionality
     * 
     * @param string $title Tab title
     * @param string $content Tab content
     * @param string $url Optional URL
     * @param bool $updateExisting Whether to update existing tab or create new one
     * @return void
     */
    public static function htmxResponse($title, $content, $url = '', $updateExisting = false) {
        if ($updateExisting) {
            $script = self::updateCurrentTab($content, $title);
        } else {
            $script = self::createTab($title, $content, $url);
        }
        
        // Set HTMX trigger header to execute the JavaScript
        header('HX-Trigger: ' . json_encode([
            'executeScript' => $script
        ]));
        
        // Return empty content since we're using events
        echo '';
    }
    
    /**
     * Generate out-of-band swap for tab bar and content
     * 
     * @param string $title Tab title
     * @param string $content Tab content
     * @param string $url Optional URL
     * @return string OOB swap HTML
     */
    public static function oobSwap($title, $content, $url = '') {
        $script = self::createTab($title, $content, $url);
        
        return '<script hx-swap-oob="afterbegin:body">' . $script . '</script>';
    }
    
    /**
     * Check if modal should create new tab or replace content
     * This can be determined by checking if there are pinned tabs
     * 
     * @return bool True if should create new tab, false if should replace
     */
    public static function shouldCreateNewTab() {
        // This would typically check session or other state
        // For now, we'll assume new tab creation unless specified otherwise
        return true;
    }
    
    /**
     * Generate JavaScript event dispatch code
     * 
     * @param string $eventName Name of the event to dispatch
     * @param array $data Data to pass with the event
     * @return string JavaScript code
     */
    private static function dispatchEvent($eventName, $data) {
        $jsonData = json_encode($data);
        return "window.dispatchEvent(new CustomEvent('{$eventName}', { detail: {$jsonData} }));";
    }
    
    /**
     * Wrap content for tab usage with HTMX attributes
     * 
     * @param string $content Content to wrap
     * @param string $title Tab title
     * @param array $htmxAttrs Additional HTMX attributes
     * @return string Wrapped content
     */
    public static function wrapContent($content, $title, $htmxAttrs = []) {
        $attrs = '';
        foreach ($htmxAttrs as $key => $value) {
            $attrs .= ' ' . htmlspecialchars($key) . '="' . htmlspecialchars($value) . '"';
        }
        
        return '<div data-tab-title="' . htmlspecialchars($title) . '"' . $attrs . '>' . $content . '</div>';
    }
    
    /**
     * Generate HTMX attributes for tab-aware buttons
     * 
     * @param string $endpoint HTMX endpoint
     * @param string $tabTitle Title for the new tab
     * @param array $additionalAttrs Additional HTMX attributes
     * @return array HTMX attributes array
     */
    public static function getTabButtonAttrs($endpoint, $tabTitle, $additionalAttrs = []) {
        return array_merge([
            'hx-get' => $endpoint,
            'hx-target' => '#modal_body',
            'hx-swap' => 'none', // We'll handle the swap via events
            'data-tab-title' => $tabTitle,
            '@click' => 'if (!showModal || !tabs.some(t => t.pinned)) { showModal = true; }'
        ], $additionalAttrs);
    }
}
