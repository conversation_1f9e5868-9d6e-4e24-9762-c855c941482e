@props([
    'title' => 'Data Sources',
    'description' => 'Manage database data sources for tables and email campaigns'
])

@php
use system\data_source_manager;

// Get data sources and statistics
$data_sources = data_source_manager::get_data_sources();
$available_tables = data_source_manager::get_available_tables();

// Group data sources by category
$grouped_sources = [];
foreach ($data_sources as $source) {
    $category = $source['category'] ?? 'other';
    if (!isset($grouped_sources[$category])) {
        $grouped_sources[$category] = [];
    }
    $grouped_sources[$category][] = $source;
}

// Calculate statistics
$total_sources = count($data_sources);
$active_sources = count(array_filter($data_sources, fn($s) => $s['status'] === 'active'));
$total_tables = count($available_tables);

$category_labels = [
    'data_table' => 'Data Tables',
    'email' => 'Email & Campaigns',
    'users' => 'User Management',
    'system' => 'System Tables',
    'autodesk' => 'Autodesk Integration',
    'other' => 'Other Tables'
];
@endphp


<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $title }}</h1>
                <p class="mt-2 text-sm text-gray-600">{{ $description }}</p>
            </div>
            <div class="flex items-center space-x-3">
                <button type="button"
                        hx-get="{{ APP_ROOT }}/api/data_sources/create_view"
                        hx-target="#content_wrapper"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Create Data Source
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c0 2.21 1.79 4 4 4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Data Sources</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($total_sources) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active Sources</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($active_sources) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Available Tables</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($total_tables) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Categories</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ count($grouped_sources) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if(empty($data_sources))
        <!-- Empty State -->
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c0 2.21 1.79 4 4 4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No data sources</h3>
            <p class="mt-1 text-sm text-gray-500">Get started by creating your first data source.</p>
            <div class="mt-6">
                <button type="button"
                        hx-get="{{ APP_ROOT }}/api/data_sources/create_view"
                        hx-target="#content_wrapper"
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Create Data Source
                </button>
            </div>
        </div>
    @else
        <!-- Data Sources by Category -->
        @foreach($grouped_sources as $category => $sources)
            <div class="mb-8">
                <div class="mb-4">
                    <h2 class="text-lg font-medium text-gray-900">{{ $category_labels[$category] ?? ucfirst($category) }}</h2>
                    <p class="text-sm text-gray-500">{{ count($sources) }} data source{{ count($sources) !== 1 ? 's' : '' }}</p>
                </div>

                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                    @foreach($sources as $source)
                        <div class="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow duration-200">
                            <div class="p-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            @if($source['category'] === 'data_table')
                                                <svg class="h-6 w-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                                </svg>
                                            @elseif($source['category'] === 'email')
                                                <svg class="h-6 w-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                </svg>
                                            @elseif($source['category'] === 'users')
                                                <svg class="h-6 w-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                                </svg>
                                            @else
                                                <svg class="h-6 w-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c0 2.21 1.79 4 4 4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z"></path>
                                                </svg>
                                            @endif
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-gray-900">{{ $source['name'] }}</h3>
                                            <p class="text-sm text-gray-500 font-mono">{{ $source['table_name'] }}</p>
                                        </div>
                                    </div>

                                    <div class="flex items-center space-x-2">
                                        @if($source['status'] === 'active')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Active
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                Inactive
                                            </span>
                                        @endif
                                    </div>
                                </div>

                                @if(!empty($source['description']))
                                    <p class="mt-3 text-sm text-gray-600">{{ $source['description'] }}</p>
                                @endif

                                <div class="mt-4 flex items-center justify-between text-xs text-gray-500">
                                    <span>Created {{ date('M j, Y', strtotime($source['created_at'])) }}</span>
                                    @if(!empty($source['filters']))
                                        <span>{{ count($source['filters']) }} filter{{ count($source['filters']) !== 1 ? 's' : '' }}</span>
                                    @endif
                                </div>

                                <div class="mt-4 flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <button type="button"
                                                hx-get="{{ APP_ROOT }}/api/data_sources/preview_view"
                                                hx-vals='{"id": {{ $source['id'] }}}'
                                                hx-target="#content_wrapper"
                                                class="text-indigo-600 hover:text-indigo-500 text-sm font-medium">
                                            Preview
                                        </button>
                                        <span class="text-gray-300">|</span>
                                        <button type="button"
                                                hx-get="{{ APP_ROOT }}/api/data_sources/edit_view"
                                                hx-replace-url="{{ APP_ROOT }}/data_sources/edit_view"
                                                hx-vals='{"id": {{ $source['id'] }}}'
                                                hx-target="#content_wrapper"
                                                class="text-indigo-600 hover:text-indigo-500 text-sm font-medium">
                                            Edit
                                        </button>
                                    </div>

                                    <button type="button"
                                            onclick="deleteDataSource({{ $source['id'] }}, '{{ $source['name'] }}')"
                                            class="text-red-600 hover:text-red-500 text-sm font-medium">
                                        Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endforeach
    @endif

    <!-- Available Tables Section -->
    @if(!empty($available_tables))
        <div class="mt-12">
            <div class="mb-6">
                <h2 class="text-lg font-medium text-gray-900">Available Database Tables</h2>
                <p class="text-sm text-gray-500">Tables that can be used to create new data sources</p>
            </div>

            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <ul class="divide-y divide-gray-200">
                    @foreach($available_tables as $table)
                        <li class="px-6 py-4 hover:bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        @if($table['category'] === 'data_table')
                                            <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                            </svg>
                                        @else
                                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c0 2.21 1.79 4 4 4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z"></path>
                                            </svg>
                                        @endif
                                    </div>
                                    <div class="ml-4">
                                        <div class="flex items-center">
                                            <p class="text-sm font-medium text-gray-900">{{ $table['display_name'] }}</p>
                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                {{ $category_labels[$table['category']] ?? ucfirst($table['category']) }}
                                            </span>
                                        </div>
                                        <div class="flex items-center mt-1 text-sm text-gray-500">
                                            <span class="font-mono">{{ $table['name'] }}</span>
                                            <span class="mx-2">•</span>
                                            <span>{{ number_format($table['row_count']) }} rows</span>
                                            <span class="mx-2">•</span>
                                            <span>{{ count($table['columns']) }} columns</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex items-center space-x-2">
                                    <button type="button"
                                            hx-get="{{ APP_ROOT }}/api/data_sources/create_view"
                                            hx-vals='{"table": "{{ $table['name'] }}"}'
                                            hx-target="#content_wrapper"
                                            class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        Create Data Source
                                    </button>
                                </div>
                            </div>
                        </li>
                    @endforeach
                </ul>
            </div>
        </div>
    @endif
</div>


<script>
async function deleteDataSource(id, name) {
    if (!confirm(`Are you sure you want to delete the data source "${name}"? This action cannot be undone.`)) {
        return;
    }
    
    try {
        const response = await fetch(`{{ APP_ROOT }}/api/data_sources/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: id })
        });
        
        const result = await response.json();
        if (result.success) {
            window.location.reload();
        } else {
            alert('Error deleting data source: ' + (result.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Delete error:', error);
        alert('Error deleting data source');
    }
}
</script>
