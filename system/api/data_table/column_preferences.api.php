<?php
namespace api\data_table\column_preferences;

use data_table\data_table;

function save($p) {
    print_rr($p,'save column preferences');
try {
    $table_name = $p['table_name'] ?? '';
    $callback = $p['callback'] ?? '';
    $hidden_columns = json_decode($p['hidden_columns'] ?? '[]', true);
    $column_order = json_decode($p['column_order'] ?? '[]', true);

    if (empty($table_name)) {
        throw new Exception('Table name is required');
    }

    // Store preferences in session for now (could be moved to database later)
    $preference_key = "column_preferences_{$table_name}";
    $_SESSION[$preference_key] = [
        'hidden' => $hidden_columns,
        'order' => $column_order,
        'updated_at' => date('Y-m-d H:i:s')
    ];

    // If callback is provided, call it to regenerate the table
    if (!empty($callback)) {

        // Prepare parameters for the callback
        $params = array_merge($p, [
            'table_name' => $table_name,
            'callback' => $callback
        ]);

        // Remove column preference parameters from the callback params
        unset($params['hidden_columns'], $params['column_order']);

        // Call the data table filter function which will handle the callback
        //$criteria = data_table::api_process_criteria($p['criteria']);
        $criteria = $p['criteria'] ?? [];
        print_rr("calling " . $p['callback'] . " criteria: " . $criteria);
        $result = $p['callback'](criteria: $criteria,just_table: true);

        // Return the updated table HTML
        echo $result;
    } else {
        // Just return success if no callback
        echo json_encode([
            'success' => true,
            'message' => 'Column preferences saved',
            'preferences' => $_SESSION[$preference_key]
        ]);
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'error' => $e->getMessage()
    ]);

}}
?>
