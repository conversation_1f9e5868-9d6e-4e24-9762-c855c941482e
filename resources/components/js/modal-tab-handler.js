// Modal Tab Handler for HTMX Integration
document.addEventListener('DOMContentLoaded', function() {
    
    // Listen for HTMX requests that should create tabs
    document.body.addEventListener('htmx:beforeRequest', function(evt) {
        const element = evt.detail.elt;
        const tabTitle = element.getAttribute('data-tab-title');
        
        if (tabTitle && element.getAttribute('hx-target') === '#modal_body') {
            // Store tab title for use in response
            evt.detail.xhr.tabTitle = tabTitle;
            evt.detail.xhr.requestUrl = evt.detail.requestConfig.path;
        }
    });
    
    // Handle HTMX responses for tab content
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        const xhr = evt.detail.xhr;
        const tabTitle = xhr.tabTitle;
        
        if (tabTitle && xhr.status === 200) {
            const content = xhr.responseText;
            const url = xhr.requestUrl || '';
            
            // Dispatch event to create/update tab
            window.dispatchEvent(new CustomEvent('modal-add-tab', {
                detail: {
                    title: tabTitle,
                    content: content,
                    url: url
                }
            }));
            
            // Prevent default HTMX swap since we handle it via Alpine.js
            evt.detail.shouldSwap = false;
        }
    });
    
    // Listen for executeScript triggers from server
    document.body.addEventListener('htmx:trigger', function(evt) {
        if (evt.detail && evt.detail.executeScript) {
            try {
                eval(evt.detail.executeScript);
            } catch (error) {
                console.error('Error executing modal tab script:', error);
            }
        }
    });

    // Alternative: Listen for custom trigger events
    document.body.addEventListener('executeScript', function(evt) {
        if (evt.detail && evt.detail.script) {
            try {
                eval(evt.detail.script);
            } catch (error) {
                console.error('Error executing modal tab script:', error);
            }
        }
    });
    
    // Handle legacy modal content loading (fallback)
    document.body.addEventListener('htmx:beforeSwap', function(evt) {
        // If target is modal_body and no tab title, use legacy behavior
        if (evt.detail.target.id === 'modal_body' && !evt.detail.xhr.tabTitle) {
            // Check if we have tabs - if so, create a new tab
            const modalElement = document.querySelector('[x-data]');
            if (modalElement && modalElement.__x && modalElement.__x.$data.tabs.length > 0) {
                // Create a tab for legacy content
                const content = evt.detail.xhr.responseText;
                window.dispatchEvent(new CustomEvent('modal-add-tab', {
                    detail: {
                        title: 'Content',
                        content: content,
                        url: evt.detail.xhr.responseURL || ''
                    }
                }));
                evt.detail.shouldSwap = false;
            } else {
                // No tabs exist, use legacy target
                evt.detail.target = document.getElementById('modal_body_legacy');
            }
        }
    });
    
    // Keyboard shortcuts for tab management
    document.addEventListener('keydown', function(evt) {
        // Only handle shortcuts when modal is open
        const modalElement = document.querySelector('[x-data]');
        if (!modalElement || !modalElement.__x || !modalElement.__x.$data.showModal) {
            return;
        }
        
        const modalData = modalElement.__x.$data;
        
        // Ctrl+W or Cmd+W to close current tab
        if ((evt.ctrlKey || evt.metaKey) && evt.key === 'w') {
            evt.preventDefault();
            if (modalData.currentTab) {
                modalData.closeTab(modalData.currentTab);
            }
        }
        
        // Ctrl+T or Cmd+T to focus on tabs (could be extended)
        if ((evt.ctrlKey || evt.metaKey) && evt.key === 't') {
            evt.preventDefault();
            // Focus on first tab button if exists
            const firstTabButton = document.querySelector('[x-data] button[x-text="tab.title"]');
            if (firstTabButton) {
                firstTabButton.focus();
            }
        }
        
        // Ctrl+1-9 to switch to specific tab
        if ((evt.ctrlKey || evt.metaKey) && evt.key >= '1' && evt.key <= '9') {
            evt.preventDefault();
            const tabIndex = parseInt(evt.key) - 1;
            if (modalData.tabs[tabIndex]) {
                modalData.switchTab(modalData.tabs[tabIndex].id);
            }
        }
    });
    
    // Handle tab navigation with arrow keys
    document.addEventListener('keydown', function(evt) {
        const activeElement = document.activeElement;
        
        // Check if we're focused on a tab button
        if (activeElement && activeElement.matches('[x-text="tab.title"]')) {
            const modalElement = document.querySelector('[x-data]');
            if (!modalElement || !modalElement.__x) return;
            
            const modalData = modalElement.__x.$data;
            const currentTabIndex = modalData.tabs.findIndex(tab => tab.id === modalData.currentTab);
            
            if (evt.key === 'ArrowLeft' && currentTabIndex > 0) {
                evt.preventDefault();
                modalData.switchTab(modalData.tabs[currentTabIndex - 1].id);
                // Focus on the new tab button
                setTimeout(() => {
                    const newTabButton = document.querySelectorAll('[x-text="tab.title"]')[currentTabIndex - 1];
                    if (newTabButton) newTabButton.focus();
                }, 10);
            } else if (evt.key === 'ArrowRight' && currentTabIndex < modalData.tabs.length - 1) {
                evt.preventDefault();
                modalData.switchTab(modalData.tabs[currentTabIndex + 1].id);
                // Focus on the new tab button
                setTimeout(() => {
                    const newTabButton = document.querySelectorAll('[x-text="tab.title"]')[currentTabIndex + 1];
                    if (newTabButton) newTabButton.focus();
                }, 10);
            }
        }
    });
    
    // Debug helper - log tab state changes
    if (window.location.search.includes('debug=tabs')) {
        document.addEventListener('modal-add-tab', function(evt) {
            console.log('Tab added:', evt.detail);
        });
        
        document.addEventListener('modal-update-tab', function(evt) {
            console.log('Tab updated:', evt.detail);
        });
    }
});
